import Bull from 'bull';
import { Redis } from 'ioredis';
import type { Redis as RedisType } from 'ioredis';
import axios from 'axios';
import { env } from '../config/env.js';
import { logger } from '../utils/logger.js';
import { AnyWebhookPayload } from '../types/index.js';

interface WebhookJobData {
  webhookUrl: string;
  payload: AnyWebhookPayload;
  webhookSecret?: string;
}

let webhookQueue: Bull.Queue<WebhookJobData>;
let redisClient: RedisType;

export async function initializeQueue() {
  redisClient = new Redis(env.REDIS_URL);
  
  webhookQueue = new Bull('webhook-delivery', {
    redis: env.REDIS_URL,
    defaultJobOptions: {
      attempts: env.WEBHOOK_RETRY_ATTEMPTS,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100, // Keep last 100 completed jobs
      removeOnFail: 50, // Keep last 50 failed jobs
    },
  });

  // Process webhook delivery jobs
  webhookQueue.process('webhook-delivery', async (job) => {
    const { webhookUrl, payload, webhookSecret } = job.data;

    try {
      // Get messageId from either old or new payload structure
      const messageId = getMessageId(payload);

      logger.debug({ webhookUrl, messageId, hasSecret: !!webhookSecret }, 'Delivering webhook');

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'User-Agent': 'EU-Email-Webhook/1.0',
        'X-Email-Webhook': 'true',
      };

      // Add HMAC signature if webhook secret is provided
      if (webhookSecret) {
        const crypto = await import('crypto');
        const payloadString = JSON.stringify(payload);
        const signature = crypto
          .createHmac('sha256', webhookSecret)
          .update(payloadString, 'utf8')
          .digest('hex');

        headers['X-Webhook-Signature'] = `sha256=${signature}`;
        headers['X-Webhook-Timestamp'] = Math.floor(Date.now() / 1000).toString();
      }

      const response = await axios.post(webhookUrl, payload, {
        timeout: env.WEBHOOK_TIMEOUT_MS,
        headers,
      });

      logger.info({
        webhookUrl,
        messageId,
        status: response.status,
        hasSignature: !!webhookSecret
      }, 'Webhook delivered successfully');

      return { status: 'delivered', httpStatus: response.status };
    } catch (error: any) {
      const messageId = getMessageId(payload);
      logger.error({
        error: error?.message,
        webhookUrl,
        messageId
      }, 'Webhook delivery failed');

      throw error; // This will trigger retry logic
    }
  });

  // Log queue events
  webhookQueue.on('completed', (job) => {
    logger.info({ jobId: job.id }, 'Webhook job completed');
  });

  webhookQueue.on('failed', (job, err) => {
    logger.error({ jobId: job.id, error: err.message }, 'Webhook job failed');
  });

  logger.info('Webhook queue initialized');
}

export async function queueWebhookDelivery(webhookUrl: string, payload: AnyWebhookPayload, webhookSecret?: string) {
  if (!webhookQueue) {
    throw new Error('Queue not initialized');
  }

  const messageId = getMessageId(payload);

  const job = await webhookQueue.add('webhook-delivery', {
    webhookUrl,
    payload,
    webhookSecret,
  }, {
    priority: 1,
    delay: 0,
  });

  logger.debug({ jobId: job.id, webhookUrl, messageId, hasSecret: !!webhookSecret }, 'Webhook queued for delivery');

  return job.id;
}

// Helper function to get messageId from either old or new payload structure
function getMessageId(payload: AnyWebhookPayload): string {
  // Check if it's the new enhanced structure
  if ('envelope' in payload && payload.envelope?.messageId) {
    return payload.envelope.messageId;
  }
  
  // Check if it's the old structure
  if ('messageId' in payload && payload.messageId) {
    return payload.messageId;
  }
  
  return 'unknown';
}

export function getQueue() {
  return webhookQueue;
}

export function getRedisClient() {
  return redisClient;
}