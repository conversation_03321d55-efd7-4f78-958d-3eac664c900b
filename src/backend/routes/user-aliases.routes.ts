import { FastifyPluginAsync } from 'fastify';
import { userAuthMiddleware } from '../lib/auth.js';
import { AliasesController } from '../controllers/user/aliases.controller.js';
import { aliasSchemas } from '../schemas/user/alias.schemas.js';

const aliasesController = new AliasesController();

export const userAliasRoutes: FastifyPluginAsync = async (fastify) => {
  const errorResponseSchema = { $ref: 'ErrorResponse#' };

  // Get all aliases for authenticated user
  fastify.get('/aliases', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Get user aliases',
      description: 'Retrieves all aliases for the authenticated user.',
      response: {
        200: aliasSchemas.AliasListResponse,
        401: { $ref: 'ErrorResponse#' },
        500: errorResponseSchema
      },
    }
  }, aliasesController.getAliases.bind(aliasesController));

  // Create new alias
  fastify.post('/aliases', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Create a new alias',
      description: 'Creates a new email alias for a verified domain.',
      body: aliasSchemas.CreateAliasRequest,
      response: {
        201: aliasSchemas.CreateAliasResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        403: errorResponseSchema,
        404: errorResponseSchema,
        409: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.createAlias.bind(aliasesController));

  // Get specific alias
  fastify.get('/aliases/:aliasId', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Get alias by ID',
      params: aliasSchemas.AliasIdParam,
      response: {
        200: aliasSchemas.AliasDetailResponse,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.getAlias.bind(aliasesController));

  // Update alias
  fastify.put('/aliases/:aliasId', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Update alias',
      params: aliasSchemas.AliasIdParam,
      body: aliasSchemas.UpdateAliasRequest,
      response: {
        200: aliasSchemas.UpdateAliasResponse,
        400: errorResponseSchema,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.updateAlias.bind(aliasesController));

  // Delete alias
  fastify.delete('/aliases/:aliasId', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Delete alias',
      params: aliasSchemas.AliasIdParam,
      response: {
        200: aliasSchemas.DeleteAliasResponse,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.deleteAlias.bind(aliasesController));

  // Get aliases for a specific domain
  fastify.get('/domains/:domainId/aliases', {
    preHandler: [userAuthMiddleware],
    schema: {
      tags: ['User Aliases'],
      summary: 'Get aliases for a domain',
      description: 'Retrieves all aliases for a specific domain.',
      params: aliasSchemas.DomainIdParam,
      response: {
        200: aliasSchemas.DomainAliasListResponse,
        401: { $ref: 'ErrorResponse#' },
        404: errorResponseSchema,
        500: errorResponseSchema
      },
    }
  }, aliasesController.getDomainAliases.bind(aliasesController));
};
