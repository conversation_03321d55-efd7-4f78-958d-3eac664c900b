<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useDataRefresh } from '@composables/useDataRefresh'
import WebhooksTable from './WebhooksTable.vue'

// State for webhooks data
const webhooks = ref([])
const isLoading = ref(true)

// Data refresh system
const { refreshState, updateData } = useDataRefresh()

// Load webhooks data
const loadWebhooks = async () => {
  try {
    isLoading.value = true
    const response = await fetch('/api/webhooks')
    const data = await response.json()
    webhooks.value = data.webhooks || []

    // Update global data store
    updateData('webhooks', data.webhooks || [])
  } catch (error) {
    console.error('Failed to load webhooks:', error)
  } finally {
    isLoading.value = false
  }
}

// Watch for refresh triggers
watch(() => refreshState.webhooks, () => {
  loadWebhooks()
})

onMounted(() => {
  loadWebhooks()
})

// Expose refresh method for parent components
defineExpose({
  refresh: loadWebhooks
})
</script>

<template>
  <div class="space-y-6">
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg"></span>
    </div>

    <!-- Webhooks table -->
    <WebhooksTable
      v-else
      :webhooks="webhooks"
      @refresh="loadWebhooks"
    />
  </div>
</template>
