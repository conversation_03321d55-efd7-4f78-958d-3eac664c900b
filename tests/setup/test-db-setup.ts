import { PrismaClient } from '@prisma/client';
import { beforeAll, afterAll, beforeEach } from '@jest/globals';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL,
    },
  },
});

export { prisma };

// Test database setup and teardown
export const setupTestDatabase = () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean database before each test
    // Order matters due to foreign key constraints
    await prisma.alias.deleteMany();
    await prisma.email.deleteMany(); 
    await prisma.domain.deleteMany();
    await prisma.webhook.deleteMany();
    await prisma.user.deleteMany();
    await prisma.auditLog.deleteMany();
  });
};

// Test data factories
export const createTestUser = async (data = {}) => {
  const bcrypt = await import('bcrypt');
  const defaultData = {
    email: `test-${Date.now()}@example.com`,
    password: await bcrypt.hash('TestPassword123!', 10),
    name: 'Test User',
    verified: true,
    ...data,
  };
  
  return prisma.user.create({ data: defaultData });
};

export const createTestWebhook = async (userId: string, data = {}) => {
  const defaultData = {
    name: `Test Webhook ${Date.now()}`,
    url: `https://example-${Date.now()}.com/webhook`,
    description: 'Test webhook for integration tests',
    verified: true,
    user: {
      connect: { id: userId }
    },
    ...data,
  };
  
  return prisma.webhook.create({ data: defaultData });
};

export const createTestDomain = async (userId: string, webhookId: string, data = {}) => {
  const defaultData = {
    domain: `test-${Date.now()}.example.com`,
    verified: true,
    verificationStatus: 'VERIFIED' as const,
    active: true,
    user: {
      connect: { id: userId }
    },
    webhook: {
      connect: { id: webhookId }
    },
    ...data,
  };
  
  return prisma.domain.create({ data: defaultData });
};

export const createTestAlias = async (domainId: string, webhookId: string, data = {}) => {
  const timestamp = Date.now();
  const defaultData = {
    email: `alias-${timestamp}@test-${timestamp}.example.com`,
    active: true,
    domain: {
      connect: { id: domainId }
    },
    webhook: {
      connect: { id: webhookId }
    },
    ...data,
  };
  
  return prisma.alias.create({ data: defaultData });
};

// Cleanup utilities
export const cleanupTestData = async (patterns = []) => {
  // Default cleanup patterns for test data
  const defaultPatterns = [
    '<EMAIL>',
    'webhook-test%',
    'test-%.example.com',
  ];
  
  const allPatterns = [...defaultPatterns, ...patterns];
  
  // Clean up test users and related data
  for (const pattern of allPatterns) {
    if (pattern.includes('@')) {
      // Email pattern - clean users
      await prisma.user.deleteMany({
        where: {
          email: { contains: pattern.replace('%', '') }
        }
      });
    } else if (pattern.includes('.')) {
      // Domain pattern
      await prisma.domain.deleteMany({
        where: {
          domain: { contains: pattern.replace('%', '') }
        }
      });
    }
  }
};
