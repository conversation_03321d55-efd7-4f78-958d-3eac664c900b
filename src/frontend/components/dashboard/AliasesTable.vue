<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Alias } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  aliases: Alias[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  toggleAlias: [aliasId: string, active: boolean]
  deleteAlias: [aliasId: string, aliasEmail: string]
  viewLogs: [domainId: string, aliasId: string]
}>()

// Helper function to check if an alias can be deleted
const canDeleteAlias = (alias: <PERSON><PERSON>): boolean => {
  // Count aliases for the same domain
  const domainAliases = props.aliases.filter(a => a.domainId === alias.domainId)

  // Cannot delete if it's the only alias for the domain
  if (domainAliases.length === 1) {
    return false
  }

  // Cannot delete catch-all if it's the only alias for the domain
  if ((alias.email === '*' || alias.email.startsWith('*@')) && domainAliases.length === 1) {
    return false
  }

  return true
}

const columns: TableColumn<Alias>[] = [
  {
    key: 'email',
    label: 'Email alias',
    sortable: true,
    render: (value, row) => {
      // Extract just the alias part (before @) or show "catch-all" for wildcards
      let displayAlias: string
      if (value === '*' || value.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (value.includes('@')) {
        displayAlias = value.split('@')[0]
      } else {
        displayAlias = value
      }

      // Disable toggle if webhook is unverified
      const isWebhookVerified = row.webhook?.verified ?? false
      const isDisabled = !isWebhookVerified
      const toggleClass = isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
      const title = isDisabled ? 'Verify webhook to enable this alias' : ''

      return `
        <div class="flex items-center space-x-3">
          <input type="checkbox"
                 class="toggle toggle-primary alias-status-toggle ${toggleClass}"
                 data-alias-id="${row.id}"
                 ${row.active ? 'checked' : ''}
                 ${isDisabled ? 'disabled' : ''}
                 title="${title}">
          <div class="text-sm font-medium text-gray-900">${displayAlias}</div>
        </div>
      `
    }
  },
  {
    key: 'domain',
    label: 'Domain',
    sortable: true,
    render: (value) => {
      return `<div class="text-sm text-gray-900">${value?.domain || value?.name || value}</div>`
    }
  },
  {
    key: 'webhook',
    label: 'Webhook',
    sortable: true,
    render: (value) => {
      if (!value?.url) return '<span class="text-gray-500">No webhook</span>'

      const verificationBadge = value.verified
        ? '<span class="badge bg-gray-200 text-gray-700 border border-gray-300 badge-sm ml-2"><div class="status status-success mr-1"></div>Verified</span>'
        : '<span class="badge bg-gray-200 text-gray-700 border border-gray-300 badge-sm ml-2 cursor-pointer underline hover:bg-gray-200" onclick="window.openModal(\'webhook-verification\', { webhookId: \'' + value.id + '\', webhookUrl: \'' + value.url + '\' })"><div class="status status-info mr-1 animate-bounce"></div>Verify now</span>'

      const displayUrl = formatWebhookUrl(value.url, 40)

      return `
        <div class="flex items-center gap-2 tooltip" data-tip="${value.url}">
          <span class="text-sm text-gray-900">${displayUrl}</span>
          ${verificationBadge}
        </div>
      `
    }
  },
  {
    key: 'actions',
    label: '',
    width: '160px',
    render: (_value, row) => {
      // Use the same logic for display name in delete confirmation
      let displayAlias: string
      if (row.email === '*' || row.email.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (row.email.includes('@')) {
        displayAlias = row.email.split('@')[0]
      } else {
        displayAlias = row.email
      }

      const isDeletable = canDeleteAlias(row)
      const buttonClass = isDeletable
        ? 'inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 transition-colors bg-white border border-red-300 rounded-md shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'
        : 'inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded-md shadow-sm cursor-not-allowed'

      const onClickHandler = isDeletable ? `onclick="deleteAlias('${row.id}', '${displayAlias}')"` : ''
      const tooltip = isDeletable ? '' : 'title="Cannot delete the last alias for a domain"'

      return `
        <div class="flex items-center justify-end space-x-2">
          <button type="button"
                  class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 transition-colors bg-white border border-blue-300 rounded-md shadow-sm hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  onclick="viewAliasLogs('${row.domainId}', '${row.id}')">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Logs
          </button>
          <button type="button"
                  class="${buttonClass}"
                  ${onClickHandler}
                  ${tooltip}
                  ${isDeletable ? '' : 'disabled'}>
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (alias: Alias, _index: number) => {
  // Handle row click if needed
}
</script>

<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
    <DataTable
      :columns="columns"
      :data="aliases"
      :loading="loading"
      empty-message="No aliases yet. Create your first alias to start receiving emails!"
      @row-click="handleRowClick"
    />
  </div>
</template>
