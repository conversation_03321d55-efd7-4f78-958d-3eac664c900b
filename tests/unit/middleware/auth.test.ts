import { describe, test, expect, jest, beforeEach } from '@jest/globals';
import { FastifyRequest, FastifyReply } from 'fastify';
import {
  adminAuthMiddleware,
  userAuthMiddleware,
  apiKeyAuthMiddleware,
} from '../../../src/backend/lib/auth';

// Mock the auth services
jest.mock('../../../src/backend/services/auth/admin-auth.service');
jest.mock('../../../src/backend/services/auth/user-auth.service');
jest.mock('../../../src/backend/utils/logger');

// Mock env config
jest.mock('../../../src/backend/config/env', () => ({
  env: {
    JWT_SECRET: 'test-jwt-secret',
    DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
  },
}));

describe('Auth Middleware', () => {
  let mockRequest: Partial<FastifyRequest>;
  let mockReply: Partial<FastifyReply>;
  let mockDone: jest.Mock;

  beforeEach(() => {
    // Create mutable mock request object
    mockRequest = {
      cookies: {},
      headers: {},
      log: {
        warn: jest.fn(),
      } as any,
    };

    // Create mock functions that return the reply object
    const statusMock = jest.fn().mockReturnThis();
    const sendMock = jest.fn().mockReturnThis();
    const redirectMock = jest.fn().mockReturnThis();
    const clearCookieMock = jest.fn().mockReturnThis();
    const setCookieMock = jest.fn().mockReturnThis();

    mockReply = {
      status: statusMock as any,
      send: sendMock as any,
      redirect: redirectMock as any,
      clearCookie: clearCookieMock as any,
      setCookie: setCookieMock as any,
      log: {
        warn: jest.fn(),
      } as any,
    };

    mockDone = jest.fn();

    jest.clearAllMocks();
  });

  // Helper function to set request properties
  const setRequestProps = (props: Partial<FastifyRequest>) => {
    Object.assign(mockRequest, props);
  };

  describe('adminAuthMiddleware', () => {
    test('should redirect to login page when no token and accessing admin area', () => {
      setRequestProps({ url: '/admin/dashboard' });
      mockRequest.cookies = {};

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(302);
      expect(mockReply.redirect).toHaveBeenCalledWith('/admin/login');
      expect(mockDone).not.toHaveBeenCalled();
    });

    test('should not redirect on login page when no token', () => {
      setRequestProps({ url: '/admin/login' });
      mockRequest.cookies = {};

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: No token provided' });
      expect(mockReply.redirect).not.toHaveBeenCalled();
    });

    test('should return 401 for API requests without token', () => {
      setRequestProps({ url: '/api/admin/users' });
      mockRequest.cookies = {};

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: No token provided' });
    });

    test('should continue when valid token provided', () => {
      mockRequest.cookies = { admin_token: 'valid-token' };

      // Mock the AdminAuthService to return success
      const { AdminAuthService } = require('../../../src/backend/services/auth/admin-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: true,
        payload: { userId: '123' }
      });
      AdminAuthService.prototype.verifyToken = mockVerifyToken;

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockDone).toHaveBeenCalled();
      expect(mockReply.status).not.toHaveBeenCalled();
    });

    test('should redirect and clear cookie on invalid token for admin pages', () => {
      setRequestProps({ url: '/admin/dashboard' });
      mockRequest.cookies = { admin_token: 'invalid-token' };

      // Mock the AdminAuthService to return failure
      const { AdminAuthService } = require('../../../src/backend/services/auth/admin-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: false,
        error: 'Invalid token'
      });
      AdminAuthService.prototype.verifyToken = mockVerifyToken;

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.clearCookie).toHaveBeenCalledWith('admin_token', { path: '/' });
      expect(mockReply.status).toHaveBeenCalledWith(302);
      expect(mockReply.redirect).toHaveBeenCalledWith('/admin/login');
    });
  });

  describe('userAuthMiddleware', () => {
    test('should redirect to login when no token and accessing dashboard', () => {
      setRequestProps({ url: '/dashboard' });
      mockRequest.cookies = {};

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(302);
      expect(mockReply.redirect).toHaveBeenCalledWith('/login');
    });

    test('should return 401 for API requests without token', () => {
      setRequestProps({ url: '/api/webhooks' });
      mockRequest.cookies = {};

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: No token provided' });
    });

    test('should continue and attach user info when valid token provided', () => {
      mockRequest.cookies = { user_token: 'valid-token' };

      // Mock the UserAuthService to return success
      const { UserAuthService } = require('../../../src/backend/services/auth/user-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: true,
        payload: { userId: 'user-123', email: '<EMAIL>' }
      });
      UserAuthService.prototype.verifyToken = mockVerifyToken;

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockDone).toHaveBeenCalled();
      expect((mockRequest as any).user).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
      });
    });

    test('should redirect and clear cookie on invalid token for dashboard pages', () => {
      setRequestProps({ url: '/dashboard/domains' });
      mockRequest.cookies = { user_token: 'invalid-token' };

      // Mock the UserAuthService to return failure
      const { UserAuthService } = require('../../../src/backend/services/auth/user-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: false,
        error: 'Invalid token'
      });
      UserAuthService.prototype.verifyToken = mockVerifyToken;

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.clearCookie).toHaveBeenCalledWith('user_token', { path: '/' });
      expect(mockReply.status).toHaveBeenCalledWith(302);
      expect(mockReply.redirect).toHaveBeenCalledWith('/login');
    });

    test('should return 401 for API requests with invalid token', () => {
      setRequestProps({ url: '/api/webhooks' });
      mockRequest.cookies = { user_token: 'invalid-token' };

      // Mock the UserAuthService to return failure
      const { UserAuthService } = require('../../../src/backend/services/auth/user-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: false,
        error: 'Invalid token'
      });
      UserAuthService.prototype.verifyToken = mockVerifyToken;

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: Invalid token' });
      expect(mockReply.redirect).not.toHaveBeenCalled();
    });
  });

  describe('apiKeyAuthMiddleware', () => {
    test('should continue when valid API key provided', () => {
      mockRequest.headers = { 'x-api-key': 'test-jwt-secret' };

      apiKeyAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockDone).toHaveBeenCalled();
      expect(mockReply.status).not.toHaveBeenCalled();
    });

    test('should return 401 when API key is missing', () => {
      mockRequest.headers = {};

      apiKeyAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ 
        error: 'Unauthorized: Invalid or missing API Key' 
      });
      expect(mockDone).not.toHaveBeenCalled();
    });

    test('should return 401 when API key is invalid', () => {
      mockRequest.headers = { 'x-api-key': 'invalid-key' };

      apiKeyAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ 
        error: 'Unauthorized: Invalid or missing API Key' 
      });
      expect(mockDone).not.toHaveBeenCalled();
    });

    test('should log warning on authentication failure', () => {
      mockRequest.headers = { 'x-api-key': 'wrong-key' };

      apiKeyAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.log.warn).toHaveBeenCalledWith(
        'API Key authentication failed for a user endpoint.'
      );
    });
  });

  describe('middleware edge cases', () => {
    test('adminAuthMiddleware should handle POST requests to admin login differently', () => {
      setRequestProps({ url: '/admin/login', method: 'POST' });
      mockRequest.cookies = {};

      adminAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: No token provided' });
      expect(mockReply.redirect).not.toHaveBeenCalled();
    });

    test('userAuthMiddleware should handle POST requests to dashboard differently', () => {
      setRequestProps({ url: '/dashboard', method: 'POST' });
      mockRequest.cookies = {};

      userAuthMiddleware(
        mockRequest as FastifyRequest,
        mockReply as FastifyReply,
        mockDone
      );

      expect(mockReply.status).toHaveBeenCalledWith(401);
      expect(mockReply.send).toHaveBeenCalledWith({ error: 'Unauthorized: No token provided' });
      expect(mockReply.redirect).not.toHaveBeenCalled();
    });

    test('should handle missing log object gracefully', () => {
      mockRequest.log = undefined;
      mockRequest.cookies = { admin_token: 'invalid-token' };

      // Mock the AdminAuthService to return failure
      const { AdminAuthService } = require('../../../src/backend/services/auth/admin-auth.service');
      const mockVerifyToken = jest.fn().mockReturnValue({
        success: false,
        error: 'Invalid token'
      });
      AdminAuthService.prototype.verifyToken = mockVerifyToken;

      // Should not throw error even without log object
      expect(() => {
        adminAuthMiddleware(
          mockRequest as FastifyRequest,
          mockReply as FastifyReply,
          mockDone
        );
      }).not.toThrow();
    });
  });
});
