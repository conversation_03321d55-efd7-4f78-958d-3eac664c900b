import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue(),tailwindcss(),],
  css: {
    postcss: './postcss.config.js',
  },
  esbuild: {
    tsconfigRaw: {
      compilerOptions: {
        baseUrl: '.',
        paths: {
          '@/*': ['./src/frontend/*'],
          '@components/*': ['./src/frontend/components/*'],
          '@composables/*': ['./src/frontend/composables/*'],
          '@stores/*': ['./src/frontend/stores/*'],
          '@types': ['./src/frontend/types/index.ts'],
          '@types/*': ['./src/frontend/types/*'],
          '@utils/*': ['./src/frontend/utils/*']
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/frontend'),
      '@components': resolve(__dirname, 'src/frontend/components'),
      '@composables': resolve(__dirname, 'src/frontend/composables'),
      '@stores': resolve(__dirname, 'src/frontend/stores'),
      '@types': resolve(__dirname, 'src/frontend/types'),
      '@utils': resolve(__dirname, 'src/frontend/utils')
    }
  },
  build: {
    outDir: 'dist/frontend',
    emptyOutDir: true,
    manifest: 'manifest.json',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/frontend/main.ts')
      }
    }
  },
  publicDir: false,
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      },
      '/admin': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
})
