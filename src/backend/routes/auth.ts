import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import bcrypt from 'bcrypt'
import jwt from 'jsonwebtoken'
import { prisma } from '../lib/prisma.js'
import { userAuthMiddleware, adminAuthMiddleware } from '../lib/auth.js'
import { getViteAssetPath, getViteAssetCss } from '../lib/vite-manifest.js'

export default async function authRoutes(fastify: FastifyInstance) {
  // =============================================================================
  // ADMIN ROUTES (JSON API - No templates)
  // =============================================================================
  
  // Admin login endpoint (JSON only)
  fastify.get('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login Endpoint',
      description: 'Admin login endpoint. Returns JSON response. Admin UI not yet implemented.',
      response: {
        '200': {
          description: 'Admin login endpoint info.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  loginEndpoint: { type: 'string' }
                }
              },
            },
          },
        },
        '302': {
          description: 'Redirects to admin dashboard if already authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Check if already logged in, redirect to dashboard if so
    if (request.cookies.admin_token) {
        try {
            return reply.status(302).redirect('/admin/dashboard');
        } catch (e) {
            reply.clearCookie('admin_token', { path: '/' });
        }
    }
    return reply.send({
      message: 'Admin login endpoint. Use POST /admin/login to authenticate.',
      loginEndpoint: 'POST /admin/login'
    });
  });

  // Admin dashboard API (JSON only)
  fastify.get('/admin/dashboard', { 
    preHandler: [adminAuthMiddleware],
    schema: {
      tags: ['Admin Management'],
      summary: 'Admin Dashboard API',
      description: 'Protected admin dashboard API endpoint. Returns JSON data.',
      security: [{ adminAuth: [] }],
      response: {
        '200': {
          description: 'Admin dashboard data.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  title: { type: 'string' },
                  stats: {
                    type: 'object',
                    properties: {
                      totalDomains: { type: 'integer' },
                      verifiedDomains: { type: 'integer' },
                      pendingDomains: { type: 'integer' },
                      todayEmails: { type: 'integer' }
                    }
                  },
                  user: {
                    type: 'object',
                    properties: {
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '302': {
          description: 'Redirects to login if not authenticated.',
        }
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    return reply.send({
      title: 'Admin Dashboard',
      stats: {
        totalDomains: 0,
        verifiedDomains: 0,
        pendingDomains: 0,
        todayEmails: 0
      },
      user: { name: 'Admin' },
      message: 'Admin dashboard API endpoint. Admin UI not yet implemented.'
    });
  });

  // Admin login POST endpoint
  fastify.post('/admin/login', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Login',
      description: 'Authenticate admin user and return JWT token.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 1 }
        }
      },
      response: {
        '200': {
          description: 'Login successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      role: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '400': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password } = request.body as { email: string; password: string };

    try {
      // For now, use hardcoded admin credentials
      // TODO: Move to database when admin system is implemented
      const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
      const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

      if (email !== ADMIN_EMAIL || password !== ADMIN_PASSWORD) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid admin credentials'
        });
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: 'admin', email: ADMIN_EMAIL, role: 'admin' },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '24h' }
      );

      // Set HTTP-only cookie
      reply.setCookie('admin_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      return reply.send({
        message: 'Admin login successful',
        user: {
          id: 'admin',
          email: ADMIN_EMAIL,
          role: 'admin'
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Admin login error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Login failed'
      });
    }
  });

  // Admin logout
  fastify.post('/admin/logout', {
    schema: {
      tags: ['Admin Authentication'],
      summary: 'Admin Logout',
      description: 'Logout admin user and clear session.',
      response: {
        '200': {
          description: 'Logout successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' }
                }
              },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    reply.clearCookie('admin_token', { path: '/' });
    return reply.send({ message: 'Admin logout successful' });
  });

  // =============================================================================
  // USER AUTHENTICATION API ENDPOINTS
  // =============================================================================

  // User registration POST endpoint
  fastify.post('/register', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Registration',
      description: 'Register a new user account.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 6 },
          name: { type: 'string' }
        }
      },
      response: {
        '201': {
          description: 'Registration successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '400': { $ref: 'ErrorResponse#' },
        '409': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password, name } = request.body as { email: string; password: string; name?: string };

    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return reply.status(409).send({
          statusCode: 409,
          error: 'Conflict',
          message: 'User with this email already exists'
        });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name: name || email.split('@')[0]
        },
        select: {
          id: true,
          email: true,
          name: true
        }
      });

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, email: user.email },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '7d' }
      );

      // Set HTTP-only cookie
      reply.setCookie('user_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      return reply.status(201).send({
        message: 'Registration successful',
        user
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Registration error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Registration failed'
      });
    }
  });

  // User login POST endpoint
  fastify.post('/login', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Login',
      description: 'Authenticate user and return JWT token.',
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 1 }
        }
      },
      response: {
        '200': {
          description: 'Login successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' },
                  user: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      email: { type: 'string' },
                      name: { type: 'string' }
                    }
                  }
                }
              },
            },
          },
        },
        '401': { $ref: 'ErrorResponse#' },
        '400': { $ref: 'ErrorResponse#' },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const { email, password } = request.body as { email: string; password: string };

    try {
      // Find user
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          name: true,
          password: true
        }
      });

      if (!user) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid email or password'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return reply.status(401).send({
          statusCode: 401,
          error: 'Unauthorized',
          message: 'Invalid email or password'
        });
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, email: user.email },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '7d' }
      );

      // Set HTTP-only cookie
      reply.setCookie('user_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });

      return reply.send({
        message: 'Login successful',
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        }
      });
    } catch (error: any) {
      fastify.log.error({ error: error?.message, stack: error.stack }, 'Login error');
      return reply.status(500).send({
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Login failed'
      });
    }
  });

  // User logout
  fastify.post('/logout', {
    schema: {
      tags: ['User Authentication'],
      summary: 'User Logout',
      description: 'Logout user and clear session.',
      response: {
        '200': {
          description: 'Logout successful.',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  message: { type: 'string' }
                }
              },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    reply.clearCookie('user_token', { path: '/' });
    return reply.send({ message: 'Logout successful' });
  });

  // =============================================================================
  // SPA ROUTES - Single Vue Application
  // =============================================================================

  // Main SPA route - serves Vue app for all client-side routing
  fastify.get('/app', {
    schema: {
      tags: ['SPA'],
      summary: 'Vue SPA Entry Point',
      description: 'Serves the Vue single-page application. Vue Router handles all client-side routing.',
      response: {
        '200': {
          description: 'Vue SPA HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Get dynamic asset paths from Vite manifest
    const jsPath = getViteAssetPath('main');
    const cssFiles = getViteAssetCss('main');

    // Generate CSS link tags
    const cssLinks = cssFiles.map(cssPath =>
      `<link rel="stylesheet" href="${cssPath}">`
    ).join('\n    ');

    // Generate HTML for Vue SPA with correct asset paths
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EU Email Webhook</title>
    ${cssLinks}
</head>
<body>
    <div id="app"></div>
    <script type="module" src="${jsPath}"></script>
</body>
</html>`;

    return reply.type('text/html').send(html);
  });

  // Catch-all route for SPA - handles all /app/* paths
  fastify.get('/app/*', {
    schema: {
      tags: ['SPA'],
      summary: 'Vue SPA Catch-All Route',
      description: 'Serves the Vue SPA for all /app/* paths. Vue Router handles client-side routing.',
      response: {
        '200': {
          description: 'Vue SPA HTML page.',
          content: {
            'text/html': {
              schema: { type: 'string' },
            },
          },
        },
      },
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    // Get dynamic asset paths from Vite manifest (same as main /app route)
    const jsPath = getViteAssetPath('main');
    const cssFiles = getViteAssetCss('main');

    // Generate CSS link tags
    const cssLinks = cssFiles.map(cssPath =>
      `<link rel="stylesheet" href="${cssPath}">`
    ).join('\n    ');

    // Same HTML as main /app route
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EU Email Webhook</title>
    ${cssLinks}
</head>
<body>
    <div id="app"></div>
    <script type="module" src="${jsPath}"></script>
</body>
</html>`;

    return reply.type('text/html').send(html);
  });

  // =============================================================================
  // LEGACY ROUTE REDIRECTS - Redirect old routes to SPA
  // =============================================================================

  // Redirect all legacy routes to SPA with hash routing
  const legacyRoutes = [
    '/login', '/register', '/dashboard', '/domains', 
    '/aliases', '/webhooks', '/logs', '/settings'
  ];
  
  legacyRoutes.forEach(route => {
    fastify.get(route, {
      schema: {
        tags: ['Legacy Redirects'],
        summary: `${route} redirect`,
        description: 'Redirects legacy route to SPA for client-side routing.',
        response: {
          '302': { description: 'Redirects to SPA.' },
        },
      }
    }, async (request: FastifyRequest, reply: FastifyReply) => {
      // For auth routes, check if already logged in
      if (['/login', '/register'].includes(route) && request.cookies.user_token) {
        return reply.status(302).redirect('/app/domains');
      }

      // For dashboard routes, check authentication
      if (!['/login', '/register'].includes(route) && !request.cookies.user_token) {
        return reply.status(302).redirect('/app/login');
      }

      return reply.status(302).redirect(`/app${route}`);
    });
  });

  fastify.log.info('Auth routes registered');
  fastify.log.info('SPA route registered at /app');
  fastify.log.info('Legacy route redirects configured');
}
