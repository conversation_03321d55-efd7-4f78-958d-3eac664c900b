/* User Dashboard Custom Styles - Optimized for Vue3 + DaisyUI */

/* Essential CSS variables */
:root {
  --color-primary: #007AFF;
  --color-success: #34C759;
  --color-warning: #FF9500;
  --color-error: #FF3B30;
}

/* Base typography */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modal overflow control */
body.overflow-hidden {
  overflow: hidden !important;
}

/* Toggles now handled by DaisyUI */

/* Tooltips now handled by DaisyUI */

/* Loading states */
.toggle-loading {
  opacity: 0.6;
  pointer-events: none;
}

.row-loading {
  opacity: 0.7;
  background-color: #f8f9fa;
}

/* Responsive design for mobile */
@media (max-width: 768px) {
  .domain-status-toggle,
  .alias-status-toggle {
    width: 36px;
    height: 22px;
  }

  .domain-status-toggle::before,
  .alias-status-toggle::before {
    width: 18px;
    height: 18px;
  }

  .domain-status-toggle:checked::before,
  .alias-status-toggle:checked::before {
    transform: translateX(14px);
  }
}
