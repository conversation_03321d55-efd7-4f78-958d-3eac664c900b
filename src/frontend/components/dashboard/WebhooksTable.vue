<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Webhook } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  webhooks: Webhook[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  verifyWebhook: [webhookId: string]
  deleteWebhook: [webhookId: string, webhookName: string]
}>()

const columns: TableColumn<Webhook>[] = [
  {
    key: 'name',
    label: 'Name',
    sortable: true,
    render: (value) => {
      return `
        <div class="tooltip" data-tip="${value}">
          <span class="text-sm font-medium text-gray-900 truncate block max-w-[150px]">${value}</span>
        </div>
      `
    }
  },
  {
    key: 'verified',
    label: 'Status',
    sortable: true,
    render: (value, row) => {
      if (value) {
        return `
          <div class="flex items-center gap-2">
            <div class="status status-success"></div>
            <span class="text-success">Verified</span>
          </div>
        `
      } else {
        return `
          <button onclick="window.openModal('webhook-verification', { webhookId: '${row.id}', webhookUrl: '${row.url}' })"
                  class="flex items-center gap-2 cursor-pointer hover:opacity-80">
            <div class="status status-info animate-bounce"></div>
            <span class="underline text-info">Verify now</span>
          </button>
        `
      }
    }
  },
  {
    key: 'url',
    label: 'URL',
    sortable: true,
    render: (value) => {
      const displayUrl = formatWebhookUrl(value, 40)

      return `
        <div class="tooltip" data-tip="${value}">
          <span class="text-sm text-gray-900">${displayUrl}</span>
        </div>
      `
    }
  },
  {
    key: 'description',
    label: 'Description',
    render: (value) => {
      if (!value) return '<span class="text-gray-500">No description</span>'
      
      return `
        <div class="tooltip" data-tip="${value}">
          <div class="text-sm text-gray-600 truncate max-w-[150px]">${value}</div>
        </div>
      `
    }
  },
  {
    key: 'actions',
    label: '',
    width: '120px',
    render: (value, row) => {
      const isInUse = (row.domainCount || 0) > 0 || (row.aliasCount || 0) > 0
      const buttonClass = isInUse
        ? 'inline-flex items-center px-3 py-1 text-xs font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded-md shadow-sm cursor-not-allowed'
        : 'inline-flex items-center px-3 py-1 text-xs font-medium text-red-700 transition-colors bg-white border border-red-300 rounded-md shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500'

      const onClickHandler = isInUse ? '' : `onclick="deleteWebhook('${row.id}', '${row.name}')"`
      const tooltip = isInUse ? `title="Cannot delete webhook in use by ${row.domainCount || 0} domain(s) and ${row.aliasCount || 0} alias(es)"` : ''

      return `
        <div class="flex items-center justify-end space-x-2">
          <button type="button"
                  class="${buttonClass}"
                  ${onClickHandler}
                  ${tooltip}
                  ${isInUse ? 'disabled' : ''}>
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (webhook: Webhook, index: number) => {
  // Handle row click if needed
}
</script>

<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
    <DataTable
      :columns="columns"
      :data="webhooks"
      :loading="loading"
      empty-message="No webhooks yet. Create your first webhook to receive email notifications!"
      @row-click="handleRowClick"
    />
  </div>
</template>
